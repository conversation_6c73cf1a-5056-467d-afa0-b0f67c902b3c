
/**
 * 会话服务
 * 现在主要作为dataService的兼容层，建议直接使用dataService
 */

import { dataService } from './dataService';

// 会话类型定义
export interface ConversationData {
  id: number;
  title: string;
  model: string;
  createdAt: string;
}

// 消息类型定义
export interface MessageData {
  id: number;
  conversationId: number;
  role: string;
  content: string;
  createdAt: string;
}

// 为了向后兼容，导出dataService的方法
export const getAllConversations = () => dataService.getAllConversations();
export const getMessagesByConversationId = (conversationId: number) => dataService.getMessagesByConversationId(conversationId);
export const createConversation = (title: string) => dataService.createConversation(title);
export const addMessage = (conversationId: number, role: string, content: string, modelId: number) => dataService.addMessage(conversationId, role, content, modelId);
export const clearAllHistory = () => dataService.clearAllHistory();

// 删除会话
export const deleteConversation = (conversationId: number) => dataService.deleteConversation(conversationId);

// AI 通信相关的函数已移至 aiService.ts
// 这里保留一些兼容性导出，但建议直接使用 aiService

export { sendMessageToAI, streamMessageToAI } from '@renderer/service/aiService';
