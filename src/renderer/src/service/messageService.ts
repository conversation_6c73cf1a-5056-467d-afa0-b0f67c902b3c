/**
 * 消息服务
 * 专门处理消息发送的业务逻辑，从chatService中分离出来
 * 提供更清晰的职责分离和更好的可测试性
 */

import { getAIConfig } from '@renderer/config/aiConfig';
import type { Message } from '@renderer/store/slices/chatSlice';
import { aiService } from './aiService';
import { dataService } from './dataService';

/**
 * 消息发送选项
 */
export interface SendMessageOptions {
  text: string;
  modelId: number;
  conversationId?: number;
  onProgress?: (chunk: string) => void;
  onComplete?: (fullResponse: string) => void;
  onError?: (error: Error) => void;
}

/**
 * 消息发送结果
 */
export interface SendMessageResult {
  success: boolean;
  conversationId: number;
  userMessageId: number;
  aiMessageId?: number;
  error?: Error;
}

/**
 * 消息服务类
 */
export class MessageService {
  private static instance: MessageService;

  private constructor() {}

  static getInstance(): MessageService {
    if (!MessageService.instance) {
      MessageService.instance = new MessageService();
    }
    return MessageService.instance;
  }

  /**
   * 发送消息的主要方法
   */
  async sendMessage(options: SendMessageOptions): Promise<SendMessageResult> {
    const { text, modelId, conversationId, onProgress, onComplete, onError } = options;

    try {
      // 1. 确保有会话ID
      const finalConversationId = conversationId || await this.createNewConversation(text);

      // 2. 添加用户消息
      const userMessageId = await this.addUserMessage(finalConversationId, text, modelId);

      // 3. 检查API配置
      const config = getAIConfig();
      if (!config.apiKey) {
        const aiMessageId = await this.handleNoApiKey(finalConversationId, modelId);
        return {
          success: true,
          conversationId: finalConversationId,
          userMessageId,
          aiMessageId
        };
      }

      // 4. 生成AI回复
      const aiMessageId = await this.generateAIResponse(
        finalConversationId,
        text,
        modelId,
        onProgress,
        onComplete
      );

      return {
        success: true,
        conversationId: finalConversationId,
        userMessageId,
        aiMessageId
      };

    } catch (error) {
      const err = error instanceof Error ? error : new Error('发送消息失败');
      onError?.(err);
      return {
        success: false,
        conversationId: conversationId || 0,
        userMessageId: 0,
        error: err
      };
    }
  }

  /**
   * 创建新会话
   */
  private async createNewConversation(text: string): Promise<number> {
    const title = text.length > 20 ? text.substring(0, 20) + '...' : text;
    return await dataService.createConversation(title);
  }

  /**
   * 添加用户消息
   */
  private async addUserMessage(conversationId: number, text: string, modelId: number): Promise<number> {
    return await dataService.addMessage(conversationId, 'user', text, modelId);
  }

  /**
   * 处理没有API Key的情况
   */
  private async handleNoApiKey(conversationId: number, modelId: number): Promise<number> {
    const noApiKeyResponse = "请先在设置中配置 API密钥才能获取真实回复。";
    return await dataService.addMessage(conversationId, 'assistant', noApiKeyResponse, modelId);
  }

  /**
   * 生成AI回复
   */
  private async generateAIResponse(
    conversationId: number,
    userMessage: string,
    modelId: number,
    onProgress?: (chunk: string) => void,
    onComplete?: (fullResponse: string) => void
  ): Promise<number> {
    let fullResponse = '';

    try {
      // 使用流式生成
      const stream = await aiService.streamText({
        prompt: this.getSystemPrompt(),
        content: userMessage
      });

      const reader = stream.getReader();

      while (true) {
        const { done, value } = await reader.read();
        if (done) break;

        fullResponse += value;
        onProgress?.(value);
      }

      onComplete?.(fullResponse);

      // 保存完整的AI回复到数据库
      return await dataService.addMessage(conversationId, 'assistant', fullResponse, modelId);

    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : 'AI响应失败';
      console.error('AI响应失败:', error);

      // 保存错误消息到数据库
      return await dataService.addMessage(conversationId, 'assistant', errorMsg, modelId);
    }
  }

  /**
   * 获取系统提示词
   */
  private getSystemPrompt(): string {
    return `你是一个有用的AI助手。请用中文回答用户的问题，保持友好和专业的语调。`;
  }

  /**
   * 批量发送消息（用于批处理场景）
   */
  async sendBatchMessages(messages: SendMessageOptions[]): Promise<SendMessageResult[]> {
    const results: SendMessageResult[] = [];

    for (const messageOptions of messages) {
      const result = await this.sendMessage(messageOptions);
      results.push(result);

      // 如果发送失败，可以选择继续或停止
      if (!result.success) {
        console.warn('批量发送中的消息失败:', result.error);
      }
    }

    return results;
  }

  /**
   * 重新发送消息
   */
  async resendMessage(
    conversationId: number,
    messageText: string,
    modelId: number,
    onProgress?: (chunk: string) => void,
    onComplete?: (fullResponse: string) => void
  ): Promise<SendMessageResult> {
    return this.sendMessage({
      text: messageText,
      modelId,
      conversationId,
      onProgress,
      onComplete
    });
  }

  /**
   * 获取会话的消息历史（用于上下文）
   */
  async getConversationHistory(conversationId: number): Promise<Message[]> {
    const messages = await dataService.getMessagesByConversationId(conversationId);
    return messages.map(msg => ({
      id: msg.id,
      text: msg.content,
      isUser: msg.role === 'user'
    }));
  }

  /**
   * 删除消息
   */
  async deleteMessage(messageId: number): Promise<boolean> {
    try {
      return await dataService.deleteMessage(messageId);
    } catch (error) {
      console.error('删除消息失败:', error);
      return false;
    }
  }

  /**
   * 编辑消息
   */
  async editMessage(messageId: number, newContent: string): Promise<boolean> {
    try {
      return await dataService.updateMessage(messageId, newContent);
    } catch (error) {
      console.error('编辑消息失败:', error);
      return false;
    }
  }
}

// 导出单例实例
export const messageService = MessageService.getInstance();

// 便捷方法导出
export const sendMessage = (options: SendMessageOptions) => messageService.sendMessage(options);
export const resendMessage = (conversationId: number, messageText: string, modelId: number, onProgress?: (chunk: string) => void, onComplete?: (fullResponse: string) => void) =>
  messageService.resendMessage(conversationId, messageText, modelId, onProgress, onComplete);
export const getConversationHistory = (conversationId: number) => messageService.getConversationHistory(conversationId);
export const deleteMessage = (messageId: number) => messageService.deleteMessage(messageId);
export const editMessage = (messageId: number, newContent: string) => messageService.editMessage(messageId, newContent);
