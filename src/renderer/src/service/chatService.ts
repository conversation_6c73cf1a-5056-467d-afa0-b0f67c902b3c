import { useStore } from '@renderer/store';
import type { Message } from '@renderer/store/slices/chatSlice';
import { messageService } from './messageService';

/**
 * 聊天业务逻辑服务
 * 现在主要作为messageService和store之间的协调层
 * 负责UI状态管理和业务流程编排
 */
export const chatService = {
  /**
   * 发送消息的核心业务逻辑
   * @param text 用户输入的消息文本
   * @param currentModelId 当前选择的模型ID
   */
  sendMessage: async (text: string, currentModelId: number) => {
    const store = useStore.getState();
    const conversationId = store.selectedConversationId;

    try {
      // 清空输入框
      store.setInputText('');

      // 添加用户消息到UI
      const tempUserMessageId = Date.now();
      const userMessage: Message = {
        id: tempUserMessageId,
        text,
        isUser: true
      };
      store.addMessage(userMessage);

      // 准备AI回复的占位消息
      const tempAiMessageId = Date.now() + 1;
      const aiPlaceholder: Message = {
        id: tempAiMessageId,
        text: '',
        isUser: false
      };
      store.addMessage(aiPlaceholder);
      store.setCurrentStreamingMessageId(tempAiMessageId);

      // 使用messageService发送消息
      const result = await messageService.sendMessage({
        text,
        modelId: currentModelId,
        conversationId: conversationId || undefined,
        onProgress: (chunk: string) => {
          // 更新流式消息内容
          store.updateStreamingMessage(tempAiMessageId, chunk);
        },
        onComplete: (fullResponse: string) => {
          // 流式完成，更新最终内容
          store.updateStreamingMessage(tempAiMessageId, fullResponse);
        },
        onError: (error: Error) => {
          // 处理错误
          store.handleSendError(error);
        }
      });

      if (result.success) {
        // 更新消息ID为数据库ID
        store.updateMessageId(tempUserMessageId, result.userMessageId);
        if (result.aiMessageId) {
          store.updateMessageId(tempAiMessageId, result.aiMessageId);
        }

        // 如果是新会话，更新会话状态
        if (!conversationId && result.conversationId) {
          store.setSelectedConversationId(result.conversationId);
          // 重新加载会话列表
          await store.loadConversations();
        }
      }

    } catch (error) {
      console.error("Error in chatService.sendMessage:", error);
      store.handleSendError(error);
    } finally {
      // 重置流式状态
      store.setStreamingState(false);
      store.setCurrentStreamingMessageId(null);
    }
  },

  /**
   * 重新发送消息
   */
  resendMessage: async (messageText: string, currentModelId: number) => {
    await chatService.sendMessage(messageText, currentModelId);
  },

  /**
   * 删除消息
   */
  deleteMessage: async (messageId: number) => {
    const store = useStore.getState();

    try {
      const success = await messageService.deleteMessage(messageId);
      if (success) {
        store.removeMessage(messageId);
      }
      return success;
    } catch (error) {
      console.error('删除消息失败:', error);
      return false;
    }
  },

  /**
   * 编辑消息
   */
  editMessage: async (messageId: number, newContent: string) => {
    const store = useStore.getState();

    try {
      const success = await messageService.editMessage(messageId, newContent);
      if (success) {
        // 更新UI中的消息内容
        store.updateStreamingMessage(messageId, newContent);
      }
      return success;
    } catch (error) {
      console.error('编辑消息失败:', error);
      return false;
    }
  },

  /**
   * 加载会话历史消息
   */
  loadConversationMessages: async (conversationId: number) => {
    const store = useStore.getState();

    try {
      const messages = await messageService.getConversationHistory(conversationId);
      store.clearMessages();
      messages.forEach(message => store.addMessage(message));
    } catch (error) {
      console.error('加载会话消息失败:', error);
    }
  }
};
