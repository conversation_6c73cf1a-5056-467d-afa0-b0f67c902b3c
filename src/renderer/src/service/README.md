# 服务层重构说明

## 重构目标

将原来分散的服务层（configService、providerService、conversationService）统一到更简洁的架构中，减少重复代码，提高可维护性。

## 新的服务架构

### 1. dataService.ts - 统一数据服务
- **职责**: 统一管理所有数据访问操作
- **功能**: 
  - Provider配置管理
  - Model配置管理
  - 会话和消息管理
  - 缓存管理
- **优势**: 
  - 单一数据访问点
  - 内置缓存机制
  - 统一错误处理
  - 减少重复代码

### 2. aiService.ts - AI服务
- **职责**: 管理AI提供者实例和AI调用
- **功能**:
  - AI提供者实例管理
  - 文本生成和流式生成
  - 连接测试
  - 模型信息获取
- **优势**:
  - 完全基于IAiProvider接口
  - 单例模式，避免重复创建实例
  - 自动处理配置变更

### 3. chatService.ts - 聊天业务逻辑
- **职责**: 编排聊天相关的业务流程
- **功能**:
  - 消息发送流程编排
  - 会话管理
  - UI状态协调
- **优势**:
  - 业务逻辑集中
  - 与UI状态解耦
  - 易于测试和维护

## 迁移状态

### ✅ 已完成
- [x] 创建dataService统一数据访问
- [x] 创建aiService管理AI调用
- [x] 更新conversationService使用dataService
- [x] 更新chatService使用新的服务架构

### 🔄 进行中
- [ ] 更新所有组件使用新的服务
- [ ] 移除旧的configService和providerService
- [ ] 更新store使用新的服务

### ⏳ 待完成
- [ ] 添加服务层单元测试
- [ ] 性能优化和缓存策略调整
- [ ] 文档更新

## 使用指南

### 推荐的使用方式

```typescript
// 1. 数据操作 - 使用dataService
import { dataService } from '@renderer/service/dataService';

// 获取Provider列表
const providers = await dataService.getAllProviders();

// 保存Provider配置
await dataService.saveProvider(provider);

// 2. AI调用 - 使用aiService
import { aiService } from '@renderer/service/aiService';

// 生成文本
const response = await aiService.generateText({
  prompt: "System prompt",
  content: "User message"
});

// 流式生成
const stream = await aiService.streamText(options);

// 3. 聊天业务 - 使用chatService
import { chatService } from '@renderer/service/chatService';

// 发送消息
await chatService.sendMessage(text, modelId);
```

### 兼容性

为了保证平滑迁移，所有旧的服务接口都保留了兼容性导出：

```typescript
// 这些导入仍然有效，但建议迁移到新的服务
import { getAllProviders } from '@renderer/service/configService'; // ❌ 旧方式
import { getAllProviders } from '@renderer/service/dataService';   // ✅ 新方式
```

## 架构优势

### 1. 减少重复代码
- configService和providerService中的重复功能已合并
- 统一的错误处理和缓存机制

### 2. 更清晰的职责分离
- dataService: 纯数据访问
- aiService: AI相关操作
- chatService: 业务逻辑编排

### 3. 更好的可测试性
- 每个服务职责单一，易于单元测试
- 依赖注入友好的设计

### 4. 更强的类型安全
- 全面使用TypeScript类型
- IAiProvider接口确保AI调用的类型安全

## 迁移建议

### 对于组件开发者
1. 优先使用新的服务接口
2. 避免直接调用window.api，通过服务层访问
3. 使用新的store结构和选择器

### 对于维护者
1. 逐步移除旧的服务文件
2. 添加服务层的单元测试
3. 监控性能，调整缓存策略

## 注意事项

1. **缓存管理**: dataService内置了缓存机制，注意在数据更新后清除相关缓存
2. **错误处理**: 所有服务都有统一的错误处理，但组件层仍需要适当的错误处理
3. **性能考虑**: aiService使用单例模式，避免频繁创建AI提供者实例
4. **类型安全**: 确保所有AI调用都通过IAiProvider接口，避免直接使用具体实现
