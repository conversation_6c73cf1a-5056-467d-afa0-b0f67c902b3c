import { Model, Provider } from '@renderer/types';

/**
 * 统一数据服务
 * 合并了原来的configService和providerService的功能
 * 提供统一的数据访问接口，简化服务层架构
 */
export class DataService {
  private static instance: DataService;

  private constructor() {}

  /**
   * 获取数据服务单例
   */
  static getInstance(): DataService {
    if (!DataService.instance) {
      DataService.instance = new DataService();
    }
    return DataService.instance;
  }

  // ==================== Provider 相关操作 ====================

  /**
   * 获取所有Provider配置
   */
  async getAllProviders(): Promise<Provider[]> {
    try {
      return await window.api.getAllProviders();
    } catch (error) {
      console.error('获取Provider列表失败:', error);
      return [];
    }
  }

  /**
   * 获取单个Provider配置
   */
  async getProvider(id: string): Promise<Provider | undefined> {
    try {
      const result = await window.api.getProvider(id);
      return result || undefined;
    } catch (error) {
      console.error(`获取Provider(ID: ${id})失败:`, error);
      return undefined;
    }
  }

  /**
   * 保存Provider配置
   */
  async saveProvider(provider: Provider): Promise<void> {
    try {
      await window.api.saveProvider(provider);
    } catch (error) {
      console.error('保存Provider配置失败:', error);
      throw error;
    }
  }

  /**
   * 删除Provider配置
   */
  async deleteProvider(providerId: string): Promise<void> {
    try {
      await window.api.deleteProvider(providerId);
    } catch (error) {
      console.error(`删除Provider(ID: ${providerId})失败:`, error);
      throw error;
    }
  }

  /**
   * 添加新Provider
   */
  async addProvider(provider: Omit<Provider, 'id' | 'models'>): Promise<number> {
    try {
      return await window.api.saveProvider({
        ...provider,
        id: 'new', // 标记为新Provider
        models: [] // 空模型列表
      });
    } catch (error) {
      console.error('添加Provider失败:', error);
      throw error;
    }
  }

  // ==================== Model 相关操作 ====================

  /**
   * 获取所有模型列表
   */
  async getAllModels(): Promise<Model[]> {
    try {
      return await window.api.getAllModels();
    } catch (error) {
      console.error('获取模型列表失败:', error);
      return [];
    }
  }

  /**
   * 根据Provider ID获取模型列表
   */
  async getModelsByProviderId(providerId: number): Promise<Model[]> {
    try {
      const allModels = await this.getAllModels();
      return allModels.filter(model => model.provider === providerId.toString());
    } catch (error) {
      console.error('获取Provider模型失败:', error);
      return [];
    }
  }

  /**
   * 获取特定模型的设置
   */
  async getModelSettings(modelId: string): Promise<Model | undefined> {
    try {
      const result = await window.api.getModelSettings(modelId);
      return result || undefined;
    } catch (error) {
      console.error(`获取模型设置(ModelID: ${modelId})失败:`, error);
      return undefined;
    }
  }

  /**
   * 保存模型设置
   */
  async saveModelSettings(model: Model): Promise<void> {
    try {
      await window.api.saveModelSettings(model);
    } catch (error) {
      console.error('保存模型设置失败:', error);
      throw error;
    }
  }

  // ==================== 会话相关操作 ====================

  /**
   * 获取所有会话
   */
  async getAllConversations() {
    try {
      return await window.api.getAllConversations();
    } catch (error) {
      console.error('获取会话列表失败:', error);
      return [];
    }
  }

  /**
   * 根据会话ID获取消息列表
   */
  async getMessagesByConversationId(conversationId: number) {
    try {
      return await window.api.getMessagesByConversationId(conversationId);
    } catch (error) {
      console.error(`获取会话消息失败(ID: ${conversationId}):`, error);
      return [];
    }
  }

  /**
   * 创建新会话
   */
  async createConversation(title: string): Promise<number> {
    try {
      return await window.api.createConversation(title);
    } catch (error) {
      console.error('创建会话失败:', error);
      throw error;
    }
  }

  /**
   * 添加消息到会话
   */
  async addMessage(conversationId: number, role: string, content: string, modelId: number): Promise<number> {
    try {
      return await window.api.addMessage(conversationId, role, content, modelId);
    } catch (error) {
      console.error('添加消息失败:', error);
      throw error;
    }
  }

  /**
   * 删除会话
   */
  async deleteConversation(conversationId: number): Promise<boolean> {
    try {
      return await window.api.deleteConversation(conversationId);
    } catch (error) {
      console.error(`删除会话失败(ID: ${conversationId}):`, error);
      return false;
    }
  }

  /**
   * 清空所有历史记录
   */
  async clearAllHistory(): Promise<boolean> {
    try {
      return await window.api.clearAllHistory();
    } catch (error) {
      console.error('清空历史记录失败:', error);
      return false;
    }
  }

  /**
   * 删除消息
   */
  async deleteMessage(messageId: number): Promise<boolean> {
    try {
      const success = await window.api.deleteMessage(messageId);
      if (success) {
        this.clearCache('messages');
      }
      return success;
    } catch (error) {
      console.error('删除消息失败:', error);
      return false;
    }
  }

  /**
   * 更新消息内容
   */
  async updateMessage(messageId: number, content: string): Promise<boolean> {
    try {
      const success = await window.api.updateMessage(messageId, content);
      if (success) {
        this.clearCache('messages');
      }
      return success;
    } catch (error) {
      console.error('更新消息失败:', error);
      return false;
    }
  }

  // ==================== 缓存管理 ====================

  private cache = new Map<string, { data: any; timestamp: number; ttl: number }>();

  /**
   * 设置缓存
   */
  private setCache(key: string, data: any, ttl: number = 5 * 60 * 1000): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl
    });
  }

  /**
   * 获取缓存
   */
  private getCache(key: string): any | null {
    const cached = this.cache.get(key);
    if (!cached) return null;

    const now = Date.now();
    if (now - cached.timestamp > cached.ttl) {
      this.cache.delete(key);
      return null;
    }

    return cached.data;
  }

  /**
   * 清除缓存
   */
  clearCache(key?: string): void {
    if (key) {
      this.cache.delete(key);
    } else {
      this.cache.clear();
    }
  }

  /**
   * 带缓存的获取Provider列表
   */
  async getAllProvidersWithCache(): Promise<Provider[]> {
    const cacheKey = 'providers';
    const cached = this.getCache(cacheKey);
    if (cached) return cached;

    const providers = await this.getAllProviders();
    this.setCache(cacheKey, providers);
    return providers;
  }

  /**
   * 带缓存的获取模型列表
   */
  async getAllModelsWithCache(): Promise<Model[]> {
    const cacheKey = 'models';
    const cached = this.getCache(cacheKey);
    if (cached) return cached;

    const models = await this.getAllModels();
    this.setCache(cacheKey, models);
    return models;
  }
}

// 导出单例实例
export const dataService = DataService.getInstance();

// 为了向后兼容，导出一些常用函数
export const getAllProviders = () => dataService.getAllProviders();
export const getProvider = (id: string) => dataService.getProvider(id);
export const saveProvider = (provider: Provider) => dataService.saveProvider(provider);
export const deleteProvider = (providerId: string) => dataService.deleteProvider(providerId);
export const getAllModels = () => dataService.getAllModels();
export const getModelsByProviderId = (providerId: number) => dataService.getModelsByProviderId(providerId);
export const getModelSettings = (modelId: string) => dataService.getModelSettings(modelId);
export const saveModelSettings = (model: Model) => dataService.saveModelSettings(model);
export const getAllConversations = () => dataService.getAllConversations();
export const getMessagesByConversationId = (conversationId: number) => dataService.getMessagesByConversationId(conversationId);
export const createConversation = (title: string) => dataService.createConversation(title);
export const addMessage = (conversationId: number, role: string, content: string, modelId: number) => dataService.addMessage(conversationId, role, content, modelId);
export const deleteConversation = (conversationId: number) => dataService.deleteConversation(conversationId);
export const clearAllHistory = () => dataService.clearAllHistory();
