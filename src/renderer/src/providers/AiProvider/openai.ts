import { openai } from "@ai-sdk/openai";
import { Model, Provider } from '@renderer/types';
import { generateText, streamText } from 'ai';
import { BaseProvider, GenerateTextOptions, ProviderInfo } from './base';

export class OpenAIProvider extends BaseProvider {
  constructor(provider: Provider) {
    super(provider);
  }

  protected getDefaultModel(): string {
    return "gpt-3.5-turbo";
  }

  async generateText(options: GenerateTextOptions): Promise<string> {
    try {
      const modelName = this.getModelName(options);
      const result = await this.withRetry(async () => {
        const { text } = await generateText({
          model: openai(modelName, {
            apiKey: this.apiKey,
            baseURL: this.host
          }),
          messages: [
            { role: "system", content: options.prompt },
            { role: "user", content: options.content }
          ],
          temperature: options.temperature,
          maxTokens: options.maxTokens
        });
        return text;
      });
      return result;
    } catch (error) {
      this.handleError(error, '生成文本');
    }
  }

  streamText(options: GenerateTextOptions): ReadableStream<string> {
    try {
      const modelName = this.getModelName(options);
      const { textStream } = streamText({
        model: openai(modelName, {
          apiKey: this.apiKey,
          baseURL: this.host
        }),
        messages: [
          { role: "system", content: options.prompt },
          { role: "user", content: options.content }
        ],
        temperature: options.temperature,
        maxTokens: options.maxTokens
      });
      return textStream;
    } catch (error) {
      this.handleError(error, '流式生成');
    }
  }

  async getModels(): Promise<Model[]> {
    try {
      // 返回当前配置的模型列表
      if (this.provider.models && this.provider.models.length > 0) {
        return this.provider.models;
      }

      // 如果没有配置模型，返回默认模型
      return [{
        id: this.getDefaultModel(),
        provider: this.provider.id,
        name: this.getDefaultModel(),
        group: 'OpenAI',
        description: 'Default OpenAI model'
      }];
    } catch (error) {
      this.handleError(error, '获取模型列表');
    }
  }

  getProviderInfo(): ProviderInfo {
    return {
      id: this.provider.id,
      name: this.provider.name,
      type: this.provider.type,
      version: '1.0.0',
      supportedFeatures: [
        'text-generation',
        'streaming',
        'chat-completion',
        'function-calling',
        'vision' // 部分模型支持
      ]
    };
  }
}

export default OpenAIProvider;
