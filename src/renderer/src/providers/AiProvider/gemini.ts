import { google } from '@ai-sdk/google';
import { Model, Provider } from '@renderer/types';
import { generateText, streamText } from 'ai';
import { BaseProvider, GenerateTextOptions, ProviderInfo } from './base';

export class GeminiProvider extends BaseProvider {
  constructor(provider: Provider) {
    super(provider);
  }

  protected getDefaultModel(): string {
    return "gemini-1.5-pro-latest";
  }

  async generateText(options: GenerateTextOptions): Promise<string> {
    try {
      const modelName = this.getModelName(options);
      const result = await this.withRetry(async () => {
        const { text } = await generateText({
          model: google(modelName),
          messages: [
            { role: "system", content: options.prompt },
            { role: "user", content: options.content }
          ],
          temperature: options.temperature,
          maxTokens: options.maxTokens
        });
        return text;
      });
      return result;
    } catch (error) {
      this.handleError(error, '生成文本');
    }
  }

  streamText(options: GenerateTextOptions): ReadableStream<string> {
    try {
      const modelName = this.getModelName(options);
      const { textStream } = streamText({
        model: google(modelName),
        messages: [
          { role: "system", content: options.prompt },
          { role: "user", content: options.content }
        ],
        temperature: options.temperature,
        maxTokens: options.maxTokens
      });
      return textStream;
    } catch (error) {
      this.handleError(error, '流式生成');
    }
  }

  async getModels(): Promise<Model[]> {
    try {
      // 返回当前配置的模型列表
      if (this.provider.models && this.provider.models.length > 0) {
        return this.provider.models;
      }

      // 如果没有配置模型，返回默认模型
      return [{
        id: this.getDefaultModel(),
        provider: this.provider.id,
        name: this.getDefaultModel(),
        group: 'Google Gemini',
        description: 'Default Gemini model'
      }];
    } catch (error) {
      this.handleError(error, '获取模型列表');
    }
  }

  getProviderInfo(): ProviderInfo {
    return {
      id: this.provider.id,
      name: this.provider.name,
      type: this.provider.type,
      version: '1.0.0',
      supportedFeatures: [
        'text-generation',
        'streaming',
        'chat-completion',
        'vision',
        'multimodal'
      ]
    };
  }
}

export default GeminiProvider;
