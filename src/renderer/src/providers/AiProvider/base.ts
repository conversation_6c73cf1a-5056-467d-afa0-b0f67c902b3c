import { formatApiHost } from '@renderer/lib/urlUtil';
import type { Model, Provider } from '@renderer/types';

/**
 * AI提供者接口定义
 * 所有AI提供者必须实现此接口，确保统一的抽象契约
 */
export interface IAiProvider {
  /**
   * 生成文本（同步方式）
   * @param options 生成选项
   * @returns Promise<string> 生成的文本
   */
  generateText(options: GenerateTextOptions): Promise<string>;

  /**
   * 流式生成文本
   * @param options 生成选项
   * @returns ReadableStream<string> 文本流
   */
  streamText(options: GenerateTextOptions): ReadableStream<string>;

  /**
   * 获取提供者支持的模型列表
   * @returns Promise<Model[]> 模型列表
   */
  getModels(): Promise<Model[]>;

  /**
   * 测试连接是否正常
   * @returns Promise<boolean> 连接是否成功
   */
  testConnection(): Promise<boolean>;

  /**
   * 获取提供者信息
   * @returns ProviderInfo 提供者基本信息
   */
  getProviderInfo(): ProviderInfo;
}

/**
 * 文本生成选项
 */
export interface GenerateTextOptions {
  prompt: string;
  content: string;
  model?: string;
  temperature?: number;
  maxTokens?: number;
  stream?: boolean;
}

/**
 * 提供者信息
 */
export interface ProviderInfo {
  id: string;
  name: string;
  type: string;
  version?: string;
  supportedFeatures: string[];
}

/**
 * 抽象基类，实现所有提供者共享的逻辑
 * 包含通用的fetch请求、错误处理、重试机制等
 */
export abstract class BaseProvider implements IAiProvider {
  protected provider: Provider;
  protected host: string;
  protected apiKey: string;

  constructor(provider: Provider) {
    this.provider = provider;
    this.validateProvider();
    this.host = this.getBaseURL();
    this.apiKey = this.getApiKey();
  }

  // 抽象方法，子类必须实现
  abstract generateText(options: GenerateTextOptions): Promise<string>;
  abstract streamText(options: GenerateTextOptions): ReadableStream<string>;
  abstract getModels(): Promise<Model[]>;

  /**
   * 测试连接 - 默认实现
   */
  async testConnection(): Promise<boolean> {
    try {
      await this.generateText({
        prompt: "System prompt for testing",
        content: "Hello, this is a connection test.",
        maxTokens: 10
      });
      return true;
    } catch (error) {
      console.error('连接测试失败:', error);
      return false;
    }
  }

  /**
   * 获取提供者信息 - 默认实现
   */
  getProviderInfo(): ProviderInfo {
    return {
      id: this.provider.id,
      name: this.provider.name,
      type: this.provider.type,
      supportedFeatures: ['text-generation', 'streaming']
    };
  }

  /**
   * 验证提供者配置
   */
  protected validateProvider(): void {
    if (!this.provider) {
      throw new Error('提供者配置不能为空');
    }

    if (!this.provider.apiKey) {
      throw new Error(`${this.provider.name || this.provider.type} API密钥不能为空`);
    }
  }

  /**
   * 获取API基础URL
   */
  public getBaseURL(): string {
    const host = this.provider.apiHost;
    return formatApiHost(host);
  }

  /**
   * 获取API密钥
   */
  public getApiKey(): string {
    const keys = this.provider.apiKey;
    if (!keys) {
      throw new Error(`${this.provider.name || this.provider.type} API密钥未配置`);
    }
    return keys;
  }

  /**
   * 获取模型名称
   */
  protected getModelName(options?: GenerateTextOptions): string {
    // 优先使用选项中指定的模型
    if (options?.model) {
      return options.model;
    }

    // 如果provider.models中有启用的模型，则使用第一个
    if (this.provider.models && this.provider.models.length > 0) {
      return this.provider.models[0].id;
    }

    // 否则返回默认模型
    return this.getDefaultModel();
  }

  /**
   * 获取默认模型 - 子类可以覆盖
   */
  protected getDefaultModel(): string {
    return '';
  }

  /**
   * 通用错误处理
   */
  protected handleError(error: any, operation: string): never {
    const errorMessage = error instanceof Error ? error.message : '未知错误';
    const providerName = this.provider.name || this.provider.type;

    console.error(`${providerName} ${operation}失败:`, error);
    throw new Error(`${providerName} ${operation}失败: ${errorMessage}`);
  }

  /**
   * 通用重试机制
   */
  protected async withRetry<T>(
    operation: () => Promise<T>,
    maxRetries: number = 3,
    delay: number = 1000
  ): Promise<T> {
    let lastError: Error;

    for (let attempt = 1; attempt <= maxRetries; attempt++) {
      try {
        return await operation();
      } catch (error) {
        lastError = error instanceof Error ? error : new Error(String(error));

        if (attempt === maxRetries) {
          break;
        }

        // 指数退避延迟
        await new Promise(resolve => setTimeout(resolve, delay * Math.pow(2, attempt - 1)));
      }
    }

    throw lastError!;
  }
}
