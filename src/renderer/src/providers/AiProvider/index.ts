/**
 * AI提供者模块统一导出
 *
 * 推荐使用方式：
 * 1. 直接使用 ProviderFactory.create() 创建提供者实例
 * 2. 使用 aiService 进行AI调用（推荐）
 * 3. 所有提供者都实现 IAiProvider 接口，确保类型安全
 */

// 导出核心接口和类型
export { BaseProvider } from './base';
export type { GenerateTextOptions, IAiProvider, ProviderInfo } from './base';

// 导出工厂类
export { default as ProviderFactory } from './providerFactory';

// 导出具体提供者实现
export { DeepseekProvider } from './deepseek';
export { GeminiProvider } from './gemini';
export { OpenAIProvider } from './openai';

// 为了向后兼容，保留默认导出（但不推荐使用）
export { default } from './providerFactory';
