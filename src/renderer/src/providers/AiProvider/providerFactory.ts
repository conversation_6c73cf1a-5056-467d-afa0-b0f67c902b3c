import { Provider } from "@renderer/types"
import { IAiProvider } from "./base"
import DeepseekProvider from "./deepseek"
import GeminiProvider from "./gemini"
import { OpenAIProvider } from "./openai"

/**
 * AI提供者工厂类
 * 负责根据配置创建对应的AI提供者实例
 * 确保返回统一的IAiProvider接口类型
 */
export default class ProviderFactory {
  /**
   * 创建AI提供者实例
   * @param provider 提供者配置
   * @returns IAiProvider 提供者实例
   * @throws Error 当提供者类型不支持时抛出错误
   */
  static create(provider: Provider): IAiProvider {
    if (!provider) {
      throw new Error('提供者配置不能为空');
    }

    if (!provider.type) {
      throw new Error('提供者类型不能为空');
    }

    switch (provider.type) {
      case 'deepseek':
        return new DeepseekProvider(provider);
      case 'gemini':
        return new GeminiProvider(provider);
      case 'openai':
      case 'anthropic':
      case 'qwenlm':
      case 'azure-openai':
        return new OpenAIProvider(provider);
      default:
        throw new Error(`不支持的提供者类型: ${provider.type}`);
    }
  }

  /**
   * 获取所有支持的提供者类型
   * @returns string[] 支持的提供者类型列表
   */
  static getSupportedTypes(): string[] {
    return ['openai', 'anthropic', 'gemini', 'qwenlm', 'azure-openai', 'deepseek'];
  }

  /**
   * 检查提供者类型是否支持
   * @param type 提供者类型
   * @returns boolean 是否支持
   */
  static isTypeSupported(type: string): boolean {
    return this.getSupportedTypes().includes(type);
  }
}