import { cn } from "@renderer/lib/utils";
import { Search } from "lucide-react";
import React from 'react';
import { Input } from "../ui/input";
import { useSidebar } from "../ui/sidebar";

interface SidebarSearchProps {
  onSearch?: (query: string) => void;
}

export function SidebarSearch({ onSearch }: SidebarSearchProps) {
  const { state } = useSidebar();
  const isCollapsed = state === "collapsed";

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (onSearch) {
      onSearch(e.target.value);
    }
  };

  return (
    <div className={cn(
      "px-3 py-4",
      isCollapsed && "flex justify-center items-center"
    )}>
      {isCollapsed ? (
        <div className="h-8 w-8 flex items-center justify-center">
          <Search className="h-4 w-4 text-muted-foreground" />
        </div>
      ) : (
        <div className="relative">
          <Input
            type="text"
            placeholder="搜索对话..."
            onChange={handleChange}
            className="pl-9 h-10 bg-transparent border-[#e5e5e5] dark:border-[#2A2A2A] text-sm rounded-lg font-medium"
          />
          <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
        </div>
      )}
    </div>
  );
}