# LiftLoom 兼容性代码清理计划

## 📋 概述

重构过程中为了确保平滑迁移，我们创建了一些兼容性代码和过渡性文件。在确认新架构稳定运行后，这些代码可以逐步清理，以简化项目结构并提高维护性。

## 🗂️ 可清理的文件和代码

### 🔴 第一优先级：立即可删除的文件

#### 1. 旧的Store文件
```
src/renderer/src/store/chatStore.ts          # 已被chatSlice.ts替代
src/renderer/src/store/providerStore.ts      # 已被settingsSlice.ts替代  
src/renderer/src/store/uiStore.ts            # 已被settingsSlice.ts替代
```

**删除条件**: ✅ 已完成 - 所有组件都已更新为使用新的统一store
**预期效果**: 减少约300行冗余代码，简化store目录结构

#### 2. 旧的Service文件
```
src/renderer/src/service/configService.ts    # 已被dataService.ts替代
src/renderer/src/service/providerService.ts  # 已被dataService.ts替代
```

**删除条件**: ✅ 已完成 - 所有功能都已迁移到dataService.ts
**预期效果**: 减少约130行重复代码，统一数据访问接口

### 🟡 第二优先级：验证后可删除的文件

#### 3. 兼容层文件
```
src/renderer/src/store/migration.ts          # 兼容层，提供旧store接口映射
```

**删除条件**: 🔄 需要验证 - 确认没有组件使用兼容层接口
**验证方法**: 
- 搜索项目中是否还有 `import { useChatStore, useProviderStore, useUIStore } from '@renderer/store/migration'`
- 运行项目确保所有功能正常

**预期效果**: 减少约200行兼容性代码

#### 4. 迁移工具文件
```
src/renderer/src/store/migrationScript.ts    # 迁移验证工具
```

**删除条件**: 🔄 需要验证 - 迁移完成后不再需要
**预期效果**: 减少约270行工具代码

### 🟢 第三优先级：长期可优化的内容

#### 5. 过渡性注释和文档
- 代码中的 `// 旧方式` 和 `// 新方式` 注释
- 临时的迁移说明注释
- 过渡期的TODO注释

#### 6. 冗余的类型定义
- 检查是否有重复的类型定义
- 清理未使用的import语句

## 📝 清理步骤指南

### 阶段一：立即清理（推荐在测试通过后执行）

```bash
# 1. 删除旧的store文件
rm src/renderer/src/store/chatStore.ts
rm src/renderer/src/store/providerStore.ts  
rm src/renderer/src/store/uiStore.ts

# 2. 删除旧的service文件
rm src/renderer/src/service/configService.ts
rm src/renderer/src/service/providerService.ts
```

### 阶段二：验证后清理（建议运行1-2周后执行）

```bash
# 1. 搜索是否还有使用兼容层的代码
grep -r "from '@renderer/store/migration'" src/renderer/src/

# 2. 如果没有找到引用，则可以删除兼容层
rm src/renderer/src/store/migration.ts
rm src/renderer/src/store/migrationScript.ts
```

### 阶段三：代码优化（持续进行）

```bash
# 1. 清理未使用的import
# 使用IDE的"Optimize Imports"功能或ESLint规则

# 2. 清理过渡性注释
# 手动检查并删除临时注释
```

## 🔍 验证清单

### 删除前验证
- [ ] 所有组件都使用 `import { useStore } from '@renderer/store'`
- [ ] 没有组件使用旧的store导入
- [ ] 没有service使用旧的configService或providerService
- [ ] 项目能正常启动和运行
- [ ] 所有功能测试通过

### 删除后验证
- [ ] 项目能正常编译
- [ ] 没有TypeScript错误
- [ ] 没有运行时错误
- [ ] 所有功能正常工作
- [ ] 性能没有下降

## 📊 清理效果预估

### 代码减少量
- **旧Store文件**: ~300行代码
- **旧Service文件**: ~130行代码  
- **兼容层文件**: ~200行代码
- **迁移工具文件**: ~270行代码
- **总计**: ~900行代码

### 文件减少量
- **删除文件数**: 6个文件
- **简化目录结构**: store和service目录更清晰

### 维护性提升
- **减少代码重复**: 消除功能重复的文件
- **简化依赖关系**: 统一的导入路径
- **提高可读性**: 更清晰的项目结构
- **降低维护成本**: 减少需要维护的代码量

## ⚠️ 注意事项

### 安全删除原则
1. **备份优先**: 删除前确保代码已提交到版本控制
2. **逐步删除**: 不要一次性删除所有文件
3. **充分测试**: 每次删除后都要进行功能测试
4. **保留记录**: 记录删除的文件和原因

### 回滚计划
如果删除后出现问题，可以通过以下方式回滚：
1. 使用Git恢复删除的文件
2. 临时使用兼容层解决紧急问题
3. 逐步排查和修复问题

## 🎯 推荐清理时间表

### 立即执行（重构完成后）
- 删除旧的store文件（chatStore.ts, providerStore.ts, uiStore.ts）
- 删除旧的service文件（configService.ts, providerService.ts）

### 1周后执行
- 验证项目稳定性
- 删除兼容层文件（migration.ts）

### 2周后执行  
- 删除迁移工具文件（migrationScript.ts）
- 清理过渡性注释

### 持续优化
- 定期检查和清理未使用的代码
- 优化import语句
- 更新文档和注释

---

**清理计划创建时间**: 2025-06-27  
**预计完成时间**: 2025-07-11  
**负责人**: 开发团队
