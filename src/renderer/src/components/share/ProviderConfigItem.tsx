import { ProviderIcon } from '@renderer/components/share/ProviderIcon';
import { Alert, AlertDescription } from '@renderer/components/ui/alert';
import { Badge } from '@renderer/components/ui/badge';
import { Button } from '@renderer/components/ui/button';
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle
} from '@renderer/components/ui/card';
import { Input } from '@renderer/components/ui/input';
import { Label } from '@renderer/components/ui/label';
import { Switch } from '@renderer/components/ui/switch';
import { DEFAULT_API_HOSTS, PROVIDER_DESCRIPTIONS, PROVIDER_NAMES } from '@renderer/config/providers';
import { Provider, ProviderType } from '@renderer/types';
import { AlertCircleIcon, CheckIcon, LoaderIcon, XIcon } from 'lucide-react';
import React, { useEffect, useState } from 'react';

interface ProviderConfigItemProps {
  provider: Provider;
}

export const ProviderConfigItem: React.FC<ProviderConfigItemProps> = ({ provider }) => {
  // 本地状态
  const [isEnabled, setIsEnabled] = useState(provider.enabled || false);
  const [apiKey, setApiKey] = useState(provider.apiKey || '');
  const [apiHost, setApiHost] = useState(provider.apiHost || DEFAULT_API_HOSTS[provider.type as ProviderType] || '');
  const [hasChanges, setHasChanges] = useState(false);

  // Store状态和操作
  // 从统一store获取状态和操作
  const updateProvider = useStore((state) => state.updateProvider);
  const testConnection = useStore((state) => state.testConnection);
  const resetConnectionStatus = useStore((state) => state.resetConnectionStatus);
  const connectionStatuses = useStore((state) => state.connectionStatuses);
  const errorMessages = useStore((state) => state.errorMessages);

  const connectionStatus = connectionStatuses[provider.id] || 'idle';
  const errorMessage = errorMessages[provider.id] || '';

  // 检查是否有未保存的更改
  useEffect(() => {
    const hasApiKeyChange = apiKey !== (provider.apiKey || '');
    const hasApiHostChange = apiHost !== (provider.apiHost || DEFAULT_API_HOSTS[provider.type as ProviderType] || '');
    const hasEnabledChange = isEnabled !== (provider.enabled || false);

    setHasChanges(hasApiKeyChange || hasApiHostChange || hasEnabledChange);
  }, [apiKey, apiHost, isEnabled, provider]);

  // 处理启用/禁用状态变更
  const handleToggleEnabled = async () => {
    const newState = !isEnabled;
    setIsEnabled(newState);

    // 如果禁用，立即保存
    if (!newState) {
      await handleSave(newState);
    }
  };

  // 处理API密钥变更
  const handleApiKeyChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setApiKey(e.target.value);
    resetConnectionStatus(provider.id);
  };

  // 处理API主机变更
  const handleApiHostChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setApiHost(e.target.value);
    resetConnectionStatus(provider.id);
  };

  // 保存配置
  const handleSave = async (enabledState?: boolean) => {
    try {
      await updateProvider({
        ...provider,
        enabled: enabledState !== undefined ? enabledState : isEnabled,
        apiKey,
        apiHost
      });
      setHasChanges(false);
    } catch (error) {
      console.error('保存配置失败:', error);
    }
  };

  // 测试连接
  const handleTestConnection = async () => {
    if (!apiKey) return;

    const testProvider: Provider = {
      ...provider,
      apiKey,
      apiHost,
      enabled: true
    };

    await testConnection(testProvider);
  };

  // 渲染连接状态
  const renderConnectionStatus = () => {
    switch (connectionStatus) {
      case 'testing':
        return (
          <div className="flex items-center text-muted-foreground">
            <LoaderIcon className="w-4 h-4 mr-2 animate-spin" />
            <span className="text-sm">测试中...</span>
          </div>
        );
      case 'success':
        return (
          <Badge variant="success" className="flex items-center gap-1">
            <CheckIcon className="w-3 h-3" />
            连接成功
          </Badge>
        );
      case 'error':
        return (
          <Badge variant="error" className="flex items-center gap-1">
            <XIcon className="w-3 h-3" />
            连接失败
          </Badge>
        );
      default:
        return null;
    }
  };

  return (
    <Card className="transition-all duration-200 hover:shadow-md">
      <CardHeader className="pb-3">
        <div className="flex justify-between items-start">
          <div className="flex items-center gap-3">
            <ProviderIcon type={provider.type as ProviderType} size="md" />
            <div>
              <CardTitle className="text-lg">
                {PROVIDER_NAMES[provider.type as ProviderType]}
              </CardTitle>
              <CardDescription className="mt-1">
                {PROVIDER_DESCRIPTIONS[provider.type as ProviderType]}
              </CardDescription>
            </div>
          </div>
          <div className="flex items-center gap-2">
            {hasChanges && (
              <Badge variant="warning" className="text-xs">
                未保存
              </Badge>
            )}
            <Switch checked={isEnabled} onCheckedChange={handleToggleEnabled} />
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        <div className="space-y-2">
          <Label htmlFor={`${provider.id}-apiKey`}>API密钥</Label>
          <Input
            id={`${provider.id}-apiKey`}
            type="password"
            value={apiKey}
            onChange={handleApiKeyChange}
            placeholder="输入API密钥"
            className="font-mono text-sm"
            disabled={!isEnabled}
          />
        </div>

        <div className="space-y-2">
          <Label htmlFor={`${provider.id}-apiHost`}>API主机地址 (可选)</Label>
          <Input
            id={`${provider.id}-apiHost`}
            type="text"
            value={apiHost}
            onChange={handleApiHostChange}
            placeholder={DEFAULT_API_HOSTS[provider.type as ProviderType]}
            className="font-mono text-sm"
            disabled={!isEnabled}
          />
        </div>

        {connectionStatus === 'error' && errorMessage && (
          <Alert variant="destructive">
            <AlertCircleIcon className="h-4 w-4" />
            <AlertDescription className="text-sm">
              {errorMessage.length > 100 ? `${errorMessage.substring(0, 100)}...` : errorMessage}
            </AlertDescription>
          </Alert>
        )}
      </CardContent>

      <CardFooter className="flex justify-between items-center pt-4">
        <div className="flex items-center">
          {renderConnectionStatus()}
        </div>

        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={handleTestConnection}
            disabled={!apiKey || !isEnabled || connectionStatus === 'testing'}
          >
            {connectionStatus === 'testing' ? (
              <>
                <LoaderIcon className="w-4 h-4 mr-2 animate-spin" />
                测试中
              </>
            ) : (
              '测试连接'
            )}
          </Button>

          <Button
            variant="default"
            size="sm"
            onClick={() => handleSave()}
            disabled={!hasChanges || !isEnabled}
          >
            保存
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
};
