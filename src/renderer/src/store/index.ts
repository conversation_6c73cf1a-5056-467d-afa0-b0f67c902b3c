import { create } from 'zustand';
import { devtools, subscribeWithSelector } from 'zustand/middleware';
import { immer } from 'zustand/middleware/immer';

// 导入各个slice
import { ChatSlice, createChatSlice } from './slices/chatSlice';
import { ConversationSlice, createConversationSlice } from './slices/conversationSlice';
import { SettingsSlice, createSettingsSlice } from './slices/settingsSlice';

/**
 * 根Store类型定义
 * 合并所有slice的状态和操作
 */
export type RootStore = ChatSlice & ConversationSlice & SettingsSlice;

/**
 * 创建根Store
 * 使用Zustand的slice模式，将不同领域的状态和操作分离到不同的slice中
 * 
 * 中间件说明：
 * - immer: 允许使用不可变更新语法
 * - devtools: 支持Redux DevTools调试
 * - subscribeWithSelector: 支持选择性订阅
 */
export const useStore = create<RootStore>()(
  devtools(
    subscribeWithSelector(
      immer((...args) => ({
        // 合并所有slice
        ...createChatSlice(...args),
        ...createConversationSlice(...args),
        ...createSettingsSlice(...args),
      }))
    ),
    {
      name: 'liftloom-store', // DevTools中显示的名称
    }
  )
);

/**
 * Store选择器工具函数
 * 提供类型安全的状态选择
 */
export const useStoreSelector = <T>(selector: (state: RootStore) => T): T => {
  return useStore(selector);
};

/**
 * 获取Store状态的工具函数
 */
export const getStoreState = (): RootStore => {
  return useStore.getState();
};

/**
 * 订阅Store变化的工具函数
 */
export const subscribeToStore = (
  listener: (state: RootStore, prevState: RootStore) => void
) => {
  return useStore.subscribe(listener);
};

/**
 * 选择性订阅Store变化的工具函数
 */
export const subscribeToStoreSelector = <T>(
  selector: (state: RootStore) => T,
  listener: (selectedState: T, previousSelectedState: T) => void
) => {
  return useStore.subscribe(selector, listener);
};

// 导出slice类型，供组件使用
export type { ChatSlice, ConversationSlice, SettingsSlice };

// 为了向后兼容，导出一些常用的选择器
export const useChatState = () => useStore((state) => ({
  messages: state.messages,
  inputText: state.inputText,
  isStreamingAIMessage: state.isStreamingAIMessage,
  currentStreamingMessageId: state.currentStreamingMessageId,
  sendMessage: state.sendMessage,
  setInputText: state.setInputText,
  addMessage: state.addMessage,
  updateStreamingMessage: state.updateStreamingMessage,
  setStreamingState: state.setStreamingState,
  setCurrentStreamingMessageId: state.setCurrentStreamingMessageId,
  updateMessageId: state.updateMessageId,
  removeMessage: state.removeMessage,
  handleSendError: state.handleSendError,
}));

export const useConversationState = () => useStore((state) => ({
  conversations: state.conversations,
  selectedConversationId: state.selectedConversationId,
  isLoadingConversations: state.isLoadingConversations,
  isLoadingMessages: state.isLoadingMessages,
  loadConversations: state.loadConversations,
  selectConversation: state.selectConversation,
  deselectConversation: state.deselectConversation,
  createNewConversationUI: state.createNewConversationUI,
  clearHistory: state.clearHistory,
}));

export const useSettingsState = () => useStore((state) => ({
  // UI设置
  colorTheme: state.colorTheme,
  isModelConfigPanelOpen: state.isModelConfigPanelOpen,
  openModelConfigPanel: state.openModelConfigPanel,
  closeModelConfigPanel: state.closeModelConfigPanel,
  toggleModelConfigPanel: state.toggleModelConfigPanel,
  setColorTheme: state.setColorTheme,
  
  // Provider设置
  providers: state.providers,
  isLoadingProviders: state.isLoadingProviders,
  connectionStatuses: state.connectionStatuses,
  errorMessages: state.errorMessages,
  loadProviders: state.loadProviders,
  updateProvider: state.updateProvider,
  deleteProviderConfig: state.deleteProviderConfig,
  testConnection: state.testConnection,
  setConnectionStatus: state.setConnectionStatus,
  resetConnectionStatus: state.resetConnectionStatus,
}));
