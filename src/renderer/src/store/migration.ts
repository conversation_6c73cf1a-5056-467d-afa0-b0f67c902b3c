/**
 * Store迁移工具
 * 提供从旧store到新RootStore的兼容层和迁移工具
 */

import { useStore, RootStore } from './index';

/**
 * 兼容层：提供与旧chatStore相同的接口
 * 这样可以在不修改组件的情况下逐步迁移
 */
export const useChatStore = {
  // 获取当前状态
  getState: (): any => {
    const state = useStore.getState();
    return {
      // 会话相关状态
      conversations: state.conversations,
      isLoadingConversations: state.isLoadingConversations,
      selectedConversationId: state.selectedConversationId,
      
      // 消息相关状态
      messages: state.messages,
      isLoadingMessages: state.isLoadingMessages,
      isStreamingAIMessage: state.isStreamingAIMessage,
      inputText: state.inputText,
      currentStreamingMessageId: state.currentStreamingMessageId,
      
      // 操作方法
      loadConversations: state.loadConversations,
      selectConversation: state.selectConversation,
      deselectConversation: state.deselectConversation,
      createNewConversationUI: state.createNewConversationUI,
      clearHistory: state.clearHistory,
      setInputText: state.setInputText,
      sendMessage: state.sendMessage,
      setStreamingState: state.setStreamingState,
      setCurrentStreamingMessageId: state.setCurrentStreamingMessageId,
      updateStreamingMessage: state.updateStreamingMessage,
      addMessage: state.addMessage,
      updateMessageId: state.updateMessageId,
      removeMessage: state.removeMessage,
      handleSendError: state.handleSendError,
      setConversations: state.setConversations,
      setSelectedConversationId: state.setSelectedConversationId,
    };
  },

  // 订阅状态变化
  subscribe: (listener: (state: any, prevState: any) => void) => {
    return useStore.subscribe(listener);
  },

  // 设置状态（用于兼容旧的setState调用）
  setState: (partial: any) => {
    const currentState = useStore.getState();
    
    // 根据传入的partial更新对应的状态
    if (partial.conversations !== undefined) {
      currentState.setConversations(partial.conversations);
    }
    if (partial.selectedConversationId !== undefined) {
      currentState.setSelectedConversationId(partial.selectedConversationId);
    }
    if (partial.messages !== undefined) {
      // 这里需要特殊处理，因为新的store不直接暴露messages的setter
      // 可以通过清空然后添加的方式来实现
      currentState.clearMessages();
      partial.messages.forEach((msg: any) => currentState.addMessage(msg));
    }
    if (partial.inputText !== undefined) {
      currentState.setInputText(partial.inputText);
    }
    if (partial.isStreamingAIMessage !== undefined) {
      currentState.setStreamingState(partial.isStreamingAIMessage);
    }
    if (partial.currentStreamingMessageId !== undefined) {
      currentState.setCurrentStreamingMessageId(partial.currentStreamingMessageId);
    }
  },
};

/**
 * 兼容层：提供与旧providerStore相同的接口
 */
export const useProviderStore = {
  getState: (): any => {
    const state = useStore.getState();
    return {
      providers: state.providers,
      isLoadingProviders: state.isLoadingProviders,
      connectionStatuses: state.connectionStatuses,
      errorMessages: state.errorMessages,
      
      loadProviders: state.loadProviders,
      updateProvider: state.updateProvider,
      deleteProviderConfig: state.deleteProviderConfig,
      testConnection: state.testConnection,
      setConnectionStatus: state.setConnectionStatus,
      resetConnectionStatus: state.resetConnectionStatus,
    };
  },

  subscribe: (listener: (state: any, prevState: any) => void) => {
    return useStore.subscribe(listener);
  },
};

/**
 * 兼容层：提供与旧uiStore相同的接口
 */
export const useUIStore = {
  getState: (): any => {
    const state = useStore.getState();
    return {
      colorTheme: state.colorTheme,
      isModelConfigPanelOpen: state.isModelConfigPanelOpen,
      
      setColorTheme: state.setColorTheme,
      openModelConfigPanel: state.openModelConfigPanel,
      closeModelConfigPanel: state.closeModelConfigPanel,
      toggleModelConfigPanel: state.toggleModelConfigPanel,
    };
  },

  subscribe: (listener: (state: any, prevState: any) => void) => {
    return useStore.subscribe(listener);
  },
};

/**
 * 迁移检查工具
 * 帮助识别哪些组件还在使用旧的store
 */
export const migrationChecker = {
  /**
   * 检查是否有组件还在使用旧的store导入
   */
  checkOldImports: () => {
    console.warn('Migration Checker: 请检查以下文件是否还在使用旧的store导入:');
    console.warn('- import { useChatStore } from "@renderer/store/chatStore"');
    console.warn('- import { useProviderStore } from "@renderer/store/providerStore"');
    console.warn('- import { useUIStore } from "@renderer/store/uiStore"');
    console.warn('建议替换为: import { useStore } from "@renderer/store"');
  },

  /**
   * 记录旧store的使用情况
   */
  logOldStoreUsage: (storeName: string, componentName?: string) => {
    console.warn(`Migration: ${componentName || 'Unknown component'} is still using old ${storeName}`);
  },
};

/**
 * 迁移工具函数
 */
export const migrationUtils = {
  /**
   * 将旧的chatStore状态迁移到新的RootStore
   */
  migrateChatStore: (oldState: any) => {
    const newStore = useStore.getState();
    
    // 迁移会话数据
    if (oldState.conversations) {
      newStore.setConversations(oldState.conversations);
    }
    
    // 迁移选中的会话
    if (oldState.selectedConversationId) {
      newStore.setSelectedConversationId(oldState.selectedConversationId);
    }
    
    // 迁移消息数据
    if (oldState.messages) {
      newStore.clearMessages();
      oldState.messages.forEach((msg: any) => newStore.addMessage(msg));
    }
    
    // 迁移输入文本
    if (oldState.inputText) {
      newStore.setInputText(oldState.inputText);
    }
    
    console.log('ChatStore migration completed');
  },

  /**
   * 将旧的providerStore状态迁移到新的RootStore
   */
  migrateProviderStore: (oldState: any) => {
    const newStore = useStore.getState();
    
    if (oldState.providers) {
      newStore.setProviders(oldState.providers);
    }
    
    console.log('ProviderStore migration completed');
  },
};
