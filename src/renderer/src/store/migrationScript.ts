/**
 * Store迁移脚本
 * 用于将组件从旧的store系统迁移到新的统一RootStore
 */

import { useStore } from './index';

/**
 * 迁移工具类
 */
export class StoreMigrationTool {
  private static instance: StoreMigrationTool;
  private migrationLog: string[] = [];

  private constructor() {}

  static getInstance(): StoreMigrationTool {
    if (!StoreMigrationTool.instance) {
      StoreMigrationTool.instance = new StoreMigrationTool();
    }
    return StoreMigrationTool.instance;
  }

  /**
   * 记录迁移日志
   */
  private log(message: string): void {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    this.migrationLog.push(logMessage);
    console.log(`🔄 Migration: ${logMessage}`);
  }

  /**
   * 获取迁移日志
   */
  getMigrationLog(): string[] {
    return [...this.migrationLog];
  }

  /**
   * 清空迁移日志
   */
  clearMigrationLog(): void {
    this.migrationLog = [];
  }

  /**
   * 检查组件是否使用了旧的store导入
   */
  checkOldStoreImports(): void {
    this.log('开始检查旧store导入...');
    
    // 这里可以添加静态分析逻辑来检查文件中的导入语句
    // 由于是运行时，我们主要提供指导信息
    
    const oldImports = [
      'import { useChatStore } from "@renderer/store/chatStore"',
      'import { useProviderStore } from "@renderer/store/providerStore"', 
      'import { useUIStore } from "@renderer/store/uiStore"',
      'import { getAllProviders } from "@renderer/service/configService"',
      'import { getAllProviders } from "@renderer/service/providerService"'
    ];

    const newImports = [
      'import { useStore } from "@renderer/store"',
      'import { useChatStore, useProviderStore, useUIStore } from "@renderer/store/migration"',
      'import { dataService } from "@renderer/service/dataService"'
    ];

    this.log('需要替换的旧导入:');
    oldImports.forEach(imp => this.log(`  ❌ ${imp}`));
    
    this.log('推荐的新导入:');
    newImports.forEach(imp => this.log(`  ✅ ${imp}`));
  }

  /**
   * 验证新store的功能完整性
   */
  async validateNewStore(): Promise<boolean> {
    this.log('开始验证新store功能...');
    
    try {
      const store = useStore.getState();
      
      // 检查所有必要的状态和方法是否存在
      const requiredChatMethods = [
        'loadConversations', 'selectConversation', 'sendMessage', 
        'setInputText', 'addMessage', 'clearMessages'
      ];
      
      const requiredConversationMethods = [
        'loadConversations', 'selectConversation', 'deselectConversation',
        'createNewConversationUI', 'clearHistory'
      ];
      
      const requiredSettingsMethods = [
        'loadProviders', 'updateProvider', 'deleteProviderConfig',
        'testConnection', 'setColorTheme'
      ];

      // 验证聊天功能
      for (const method of requiredChatMethods) {
        if (typeof (store as any)[method] !== 'function') {
          this.log(`❌ 缺少聊天方法: ${method}`);
          return false;
        }
      }
      this.log('✅ 聊天功能验证通过');

      // 验证会话功能
      for (const method of requiredConversationMethods) {
        if (typeof (store as any)[method] !== 'function') {
          this.log(`❌ 缺少会话方法: ${method}`);
          return false;
        }
      }
      this.log('✅ 会话功能验证通过');

      // 验证设置功能
      for (const method of requiredSettingsMethods) {
        if (typeof (store as any)[method] !== 'function') {
          this.log(`❌ 缺少设置方法: ${method}`);
          return false;
        }
      }
      this.log('✅ 设置功能验证通过');

      // 验证状态结构
      const requiredStates = [
        'messages', 'conversations', 'providers', 'inputText',
        'isStreamingAIMessage', 'selectedConversationId', 'colorTheme'
      ];

      for (const state of requiredStates) {
        if (!((store as any).hasOwnProperty(state))) {
          this.log(`❌ 缺少状态: ${state}`);
          return false;
        }
      }
      this.log('✅ 状态结构验证通过');

      this.log('🎉 新store功能验证完全通过!');
      return true;

    } catch (error) {
      this.log(`❌ 验证过程中出错: ${error}`);
      return false;
    }
  }

  /**
   * 生成迁移指南
   */
  generateMigrationGuide(): string {
    const guide = `
# Store迁移指南

## 1. 导入语句迁移

### 旧方式 ❌
\`\`\`typescript
import { useChatStore } from '@renderer/store/chatStore';
import { useProviderStore } from '@renderer/store/providerStore';
import { useUIStore } from '@renderer/store/uiStore';
\`\`\`

### 新方式 ✅
\`\`\`typescript
// 方式1: 使用统一store
import { useStore } from '@renderer/store';

// 方式2: 使用兼容层（过渡期）
import { useChatStore, useProviderStore, useUIStore } from '@renderer/store/migration';
\`\`\`

## 2. 状态访问迁移

### 旧方式 ❌
\`\`\`typescript
const chatStore = useChatStore();
const messages = chatStore.messages;
const sendMessage = chatStore.sendMessage;
\`\`\`

### 新方式 ✅
\`\`\`typescript
const store = useStore();
const messages = store.messages;
const sendMessage = store.sendMessage;

// 或者使用选择器
const messages = useStore(state => state.messages);
const sendMessage = useStore(state => state.sendMessage);
\`\`\`

## 3. 服务层迁移

### 旧方式 ❌
\`\`\`typescript
import { getAllProviders } from '@renderer/service/configService';
import { getAllProviders as getProviders } from '@renderer/service/providerService';
\`\`\`

### 新方式 ✅
\`\`\`typescript
import { dataService } from '@renderer/service/dataService';

// 使用统一的数据服务
const providers = await dataService.getAllProviders();
\`\`\`

## 4. 迁移检查清单

- [ ] 更新所有import语句
- [ ] 替换store访问方式
- [ ] 更新服务层调用
- [ ] 测试组件功能
- [ ] 移除旧的store文件引用

## 5. 常见问题

### Q: 如何处理跨slice的状态更新？
A: 使用RootStore的统一接口，避免直接跨slice操作。

### Q: 兼容层会一直保留吗？
A: 兼容层是过渡期方案，建议尽快迁移到新的统一store。

### Q: 如何调试新的store？
A: 新store集成了Redux DevTools，可以在浏览器开发者工具中查看状态变化。
`;

    return guide;
  }

  /**
   * 执行完整的迁移检查
   */
  async runFullMigrationCheck(): Promise<void> {
    this.log('🚀 开始完整的迁移检查...');
    
    // 1. 检查旧导入
    this.checkOldStoreImports();
    
    // 2. 验证新store
    const isValid = await this.validateNewStore();
    
    if (isValid) {
      this.log('✅ 迁移检查完成，新store可以正常使用');
      this.log('📖 请查看迁移指南了解详细的迁移步骤');
    } else {
      this.log('❌ 迁移检查失败，请修复上述问题后重试');
    }
    
    // 3. 输出迁移指南
    console.log(this.generateMigrationGuide());
  }
}

// 导出单例实例
export const migrationTool = StoreMigrationTool.getInstance();

// 便捷方法
export const runMigrationCheck = () => migrationTool.runFullMigrationCheck();
export const getMigrationGuide = () => migrationTool.generateMigrationGuide();
export const validateStore = () => migrationTool.validateNewStore();
