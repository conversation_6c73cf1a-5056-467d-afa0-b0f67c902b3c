import { Provider } from '@renderer/types';
import { StateCreator } from 'zustand';

// 连接状态类型
export type ConnectionStatus = 'idle' | 'testing' | 'success' | 'error';

// 主题类型
export type ColorTheme = 'light' | 'dark' | 'system';

/**
 * 设置相关的状态和操作
 */
export interface SettingsSlice {
  // UI设置状态
  colorTheme: ColorTheme;
  isModelConfigPanelOpen: boolean;

  // Provider设置状态
  providers: Provider[];
  isLoadingProviders: boolean;
  connectionStatuses: Record<string, ConnectionStatus>;
  errorMessages: Record<string, string>;

  // UI设置操作
  setColorTheme: (theme: ColorTheme) => void;
  openModelConfigPanel: () => void;
  closeModelConfigPanel: () => void;
  toggleModelConfigPanel: () => void;

  // Provider设置操作
  loadProviders: () => Promise<void>;
  updateProvider: (provider: Provider) => Promise<void>;
  deleteProviderConfig: (providerId: string) => Promise<void>;
  testConnection: (provider: Provider) => Promise<void>;
  setConnectionStatus: (providerId: string, status: ConnectionStatus, error?: string) => void;
  resetConnectionStatus: (providerId: string) => void;
  setProviders: (providers: Provider[]) => void;
}

/**
 * 创建设置slice
 */
export const createSettingsSlice: StateCreator<
  SettingsSlice,
  [["zustand/immer", never], ["zustand/devtools", never], ["zustand/subscribeWithSelector", never]],
  [],
  SettingsSlice
> = (set, get) => ({
  // UI设置初始状态
  colorTheme: 'system',
  isModelConfigPanelOpen: false,

  // Provider设置初始状态
  providers: [],
  isLoadingProviders: false,
  connectionStatuses: {},
  errorMessages: {},

  // UI设置操作
  setColorTheme: (theme: ColorTheme) => {
    set((state) => {
      state.colorTheme = theme;
    });
  },

  openModelConfigPanel: () => {
    set((state) => {
      state.isModelConfigPanelOpen = true;
    });

    // 当打开模型配置面板时，需要清空聊天状态
    // 这里通过get()获取当前store的其他方法来实现跨slice交互
    const store = get() as any;
    if (store.deselectConversation) {
      store.deselectConversation();
    }
  },

  closeModelConfigPanel: () => {
    set((state) => {
      state.isModelConfigPanelOpen = false;
    });
  },

  toggleModelConfigPanel: () => {
    const currentState = get();
    const opening = !currentState.isModelConfigPanelOpen;

    set((state) => {
      state.isModelConfigPanelOpen = opening;
    });

    // 如果是打开面板，需要清空聊天状态
    if (opening) {
      const store = get() as any;
      if (store.deselectConversation) {
        store.deselectConversation();
      }
    }
  },

  // Provider设置操作
  loadProviders: async () => {
    set((state) => {
      state.isLoadingProviders = true;
    });

    try {
      const providers = await dataService.getAllProviders();

      set((state) => {
        state.providers = providers;
        state.isLoadingProviders = false;
      });
    } catch (error) {
      console.error('加载Provider配置失败:', error);
      set((state) => {
        state.isLoadingProviders = false;
      });
    }
  },

  updateProvider: async (provider: Provider) => {
    try {
      await dataService.saveProvider(provider);

      set((state) => {
        const index = state.providers.findIndex(p => p.id === provider.id);
        if (index !== -1) {
          state.providers[index] = provider;
        } else {
          state.providers.push(provider);
        }
      });

      // 清除相关缓存
      dataService.clearCache('providers');
    } catch (error) {
      console.error('更新Provider配置失败:', error);
      throw error;
    }
  },

  deleteProviderConfig: async (providerId: string) => {
    try {
      await dataService.deleteProvider(providerId);

      set((state) => {
        state.providers = state.providers.filter(p => p.id !== providerId);
        delete state.connectionStatuses[providerId];
        delete state.errorMessages[providerId];
      });

      // 清除相关缓存
      dataService.clearCache('providers');
    } catch (error) {
      console.error('删除Provider配置失败:', error);
      throw error;
    }
  },

  testConnection: async (provider: Provider) => {
    const { setConnectionStatus } = get();

    try {
      setConnectionStatus(provider.id, 'testing');

      // 动态导入ProviderFactory以避免循环依赖
      const { default: ProviderFactory } = await import('@renderer/providers/AiProvider/providerFactory');

      // 创建临时Provider对象用于测试
      const testProvider: Provider = {
        ...provider,
        enabled: true
      };

      // 使用ProviderFactory创建Provider实例
      const providerInstance = ProviderFactory.create(testProvider);

      // 使用新的testConnection方法进行连接测试
      const isConnected = await providerInstance.testConnection();

      if (isConnected) {
        setConnectionStatus(provider.id, 'success');
      } else {
        setConnectionStatus(provider.id, 'error', '连接测试失败');
      }
    } catch (error) {
      console.error('连接测试失败:', error);
      const errorMessage = error instanceof Error ? error.message : '未知错误';
      setConnectionStatus(provider.id, 'error', errorMessage);
    }
  },

  setConnectionStatus: (providerId: string, status: ConnectionStatus, error?: string) => {
    set((state) => {
      state.connectionStatuses[providerId] = status;
      if (error) {
        state.errorMessages[providerId] = error;
      } else {
        delete state.errorMessages[providerId];
      }
    });
  },

  resetConnectionStatus: (providerId: string) => {
    set((state) => {
      state.connectionStatuses[providerId] = 'idle';
      delete state.errorMessages[providerId];
    });
  },

  setProviders: (providers: Provider[]) => {
    set((state) => {
      state.providers = providers;
    });
  },
});
