import { formatTime } from '@renderer/lib/dateUtil';
import { StateCreator } from 'zustand';

// 会话类型定义
export interface Conversation {
  id: number;
  title: string;
  time: string;
  selected: boolean;
}

/**
 * 会话相关的状态和操作
 */
export interface ConversationSlice {
  // 状态
  conversations: Conversation[];
  selectedConversationId: number | null;
  isLoadingConversations: boolean;
  isLoadingMessages: boolean;

  // 操作
  loadConversations: () => Promise<void>;
  selectConversation: (conversationId: number) => Promise<void>;
  deselectConversation: () => void;
  createNewConversationUI: () => void;
  clearHistory: () => Promise<void>;
  setConversations: (conversations: Conversation[]) => void;
  setSelectedConversationId: (id: number | null) => void;
}

/**
 * 创建会话slice
 */
export const createConversationSlice: StateCreator<
  ConversationSlice,
  [["zustand/immer", never], ["zustand/devtools", never], ["zustand/subscribeWithSelector", never]],
  [],
  ConversationSlice
> = (set, get) => ({
  // 初始状态
  conversations: [],
  selectedConversationId: null,
  isLoadingConversations: false,
  isLoadingMessages: false,

  // 操作
  loadConversations: async () => {
    set((state) => {
      state.isLoadingConversations = true;
    });

    try {
      const conversationsData = await dataService.getAllConversations();
      const conversations: Conversation[] = conversationsData.map(conv => ({
        id: conv.id,
        title: conv.title,
        time: formatTime(conv.createdAt),
        selected: false
      }));

      set((state) => {
        state.conversations = conversations;
        state.isLoadingConversations = false;
      });
    } catch (error) {
      console.error('加载会话列表失败:', error);
      set((state) => {
        state.isLoadingConversations = false;
      });
    }
  },

  selectConversation: async (conversationId: number) => {
    set((state) => {
      state.isLoadingMessages = true;
      // 更新选中状态
      state.conversations = state.conversations.map(conv => ({
        ...conv,
        selected: conv.id === conversationId
      }));
      state.selectedConversationId = conversationId;
    });

    try {
      const messagesData = await dataService.getMessagesByConversationId(conversationId);

      // 转换消息格式并更新到聊天slice
      // 注意：这里需要访问其他slice的状态，在实际实现中可能需要调整
      const messages = messagesData.map(msg => ({
        id: msg.id,
        text: msg.content,
        isUser: msg.role === 'user'
      }));

      set((state) => {
        // 这里需要更新聊天slice的messages
        // 在实际的RootStore中，这会通过合并的状态来实现
        (state as any).messages = messages;
        state.isLoadingMessages = false;
      });
    } catch (error) {
      console.error('加载消息失败:', error);
      set((state) => {
        state.isLoadingMessages = false;
      });
    }
  },

  deselectConversation: () => {
    set((state) => {
      state.conversations = state.conversations.map(conv => ({
        ...conv,
        selected: false
      }));
      state.selectedConversationId = null;
      // 清空消息
      (state as any).messages = [];
    });
  },

  createNewConversationUI: () => {
    set((state) => {
      // 取消所有会话的选中状态
      state.conversations = state.conversations.map(conv => ({
        ...conv,
        selected: false
      }));
      state.selectedConversationId = null;
      // 清空消息和输入框
      (state as any).messages = [];
      (state as any).inputText = '';
    });
  },

  clearHistory: async () => {
    try {
      const success = await dataService.clearAllHistory();
      if (success) {
        set((state) => {
          state.conversations = [];
          state.selectedConversationId = null;
          (state as any).messages = [];
          (state as any).inputText = '';
        });
      }
    } catch (error) {
      console.error('清空历史记录失败:', error);
    }
  },

  setConversations: (conversations: Conversation[]) => {
    set((state) => {
      state.conversations = conversations;
    });
  },

  setSelectedConversationId: (id: number | null) => {
    set((state) => {
      state.selectedConversationId = id;
    });
  },
});
