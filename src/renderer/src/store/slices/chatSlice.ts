import { chatService } from '@renderer/service/chatService';
import { StateCreator } from 'zustand';

// 消息类型定义
export interface Message {
  id: number;
  text: string;
  isUser: boolean;
}

/**
 * 聊天相关的状态和操作
 */
export interface ChatSlice {
  // 状态
  messages: Message[];
  inputText: string;
  isStreamingAIMessage: boolean;
  currentStreamingMessageId: number | null;

  // 操作
  sendMessage: (text: string, currentModelId: number) => Promise<void>;
  setInputText: (text: string) => void;
  addMessage: (message: Message) => void;
  updateStreamingMessage: (messageId: number, text: string) => void;
  setStreamingState: (isStreaming: boolean) => void;
  setCurrentStreamingMessageId: (messageId: number | null) => void;
  updateMessageId: (oldId: number, newId: number) => void;
  removeMessage: (messageId: number) => void;
  handleSendError: (error: unknown) => void;
  clearMessages: () => void;
}

/**
 * 创建聊天slice
 */
export const createChatSlice: StateCreator<
  ChatSlice,
  [["zustand/immer", never], ["zustand/devtools", never], ["zustand/subscribeWithSelector", never]],
  [],
  ChatSlice
> = (set, get) => ({
  // 初始状态
  messages: [],
  inputText: '',
  isStreamingAIMessage: false,
  currentStreamingMessageId: null,

  // 操作
  sendMessage: async (text: string, currentModelId: number) => {
    try {
      // 设置流式状态
      set((state) => {
        state.isStreamingAIMessage = true;
      });

      // 调用聊天服务
      await chatService.sendMessage(text, currentModelId);
    } catch (error) {
      console.error('发送消息失败:', error);
      get().handleSendError(error);
    }
  },

  setInputText: (text: string) => {
    set((state) => {
      state.inputText = text;
    });
  },

  addMessage: (message: Message) => {
    set((state) => {
      state.messages.push(message);
    });
  },

  updateStreamingMessage: (messageId: number, text: string) => {
    set((state) => {
      const messageIndex = state.messages.findIndex(msg => msg.id === messageId);
      if (messageIndex !== -1) {
        state.messages[messageIndex].text = text;
      }
    });
  },

  setStreamingState: (isStreaming: boolean) => {
    set((state) => {
      state.isStreamingAIMessage = isStreaming;
    });
  },

  setCurrentStreamingMessageId: (messageId: number | null) => {
    set((state) => {
      state.currentStreamingMessageId = messageId;
    });
  },

  updateMessageId: (oldId: number, newId: number) => {
    set((state) => {
      const messageIndex = state.messages.findIndex(msg => msg.id === oldId);
      if (messageIndex !== -1) {
        state.messages[messageIndex].id = newId;
      }
    });
  },

  removeMessage: (messageId: number) => {
    set((state) => {
      state.messages = state.messages.filter(msg => msg.id !== messageId);
    });
  },

  handleSendError: (error: unknown) => {
    console.error('聊天错误:', error);
    set((state) => {
      state.isStreamingAIMessage = false;
      state.currentStreamingMessageId = null;
    });

    // 可以在这里添加错误消息到UI
    const errorMessage = error instanceof Error ? error.message : '发送消息失败';
    const errorMsg: Message = {
      id: Date.now(),
      text: `错误: ${errorMessage}`,
      isUser: false
    };

    set((state) => {
      state.messages.push(errorMsg);
    });
  },

  clearMessages: () => {
    set((state) => {
      state.messages = [];
      state.inputText = '';
      state.isStreamingAIMessage = false;
      state.currentStreamingMessageId = null;
    });
  },
});
